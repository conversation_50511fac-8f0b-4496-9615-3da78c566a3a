@echo off
setlocal enabledelayedexpansion
echo ========================================
echo     AppRhyme Release Build Script
echo ========================================
echo.

echo 1. Cleaning build cache...
call flutter clean
if %errorlevel% neq 0 (
    echo Error: Flutter clean failed
    pause
    exit /b 1
)

echo.
echo 2. Getting dependencies...
call flutter pub get
if %errorlevel% neq 0 (
    echo Error: Flutter pub get failed
    pause
    exit /b 1
)

echo.
echo 3. Building Rust backend...
cd rust
call cargo build --release
if %errorlevel% neq 0 (
    echo Error: Rust build failed
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo 4. Building Android APK Release...
call flutter build apk --release --target-platform android-arm64 --split-per-abi
if %errorlevel% neq 0 (
    echo Error: Flutter APK build failed
    pause
    exit /b 1
)

echo.
echo 5. Checking build results...
set APK_PATH=build\app\outputs\flutter-apk\app-arm64-v8a-release.apk
if exist "%APK_PATH%" (
    echo Success: APK built at %APK_PATH%
    for %%A in ("%APK_PATH%") do (
        echo   File size: %%~zA bytes
        set /a SIZE_MB=%%~zA/1024/1024
        echo   File size: !SIZE_MB! MB
    )
) else (
    echo Error: APK file not found at %APK_PATH%
    pause
    exit /b 1
)

echo.
echo 6. Signing APK...
call sign_apk.bat
if %errorlevel% neq 0 (
    echo Warning: APK signing may have failed, but build is complete
)

echo.
echo ========================================
echo     Build Complete!
echo ========================================
echo.
echo Output files:
echo   Unsigned: %APK_PATH%
if exist "build\app\outputs\flutter-apk\app-arm64-v8a-release-signed.apk" (
    echo   Signed: build\app\outputs\flutter-apk\app-arm64-v8a-release-signed.apk
)
echo.
echo Version: AppRhyme v1.0.9
echo Build time: %date% %time%
echo.
pause
