import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:flutter_rust_bridge/flutter_rust_bridge_for_generated.dart';

/// 网易云音乐数据模型
class NeteaseMusicData {
  final String id;
  final String name;
  final List<NeteaseArtist> artists;
  final NeteaseAlbum album;
  final String duration;

  NeteaseMusicData({
    required this.id,
    required this.name,
    required this.artists,
    required this.album,
    required this.duration,
  });

  factory NeteaseMusicData.fromJson(Map<String, dynamic> json) {
    return NeteaseMusicData(
      id: json['id']?.toString() ?? '0',
      name: json['name']?.toString() ?? '未知歌曲',
      artists: (json['artists'] as List<dynamic>?)
              ?.map((artist) => NeteaseArtist.fromJson(artist))
              .toList() ??
          [],
      album: NeteaseAlbum.fromJson(json['album'] ?? {}),
      duration: json['duration']?.toString() ?? '00:00',
    );
  }

  /// 转换为MusicInfo
  MusicInfo toMusicInfo() {
    return MusicInfo(
      id: PlatformInt64.parse(id),
      source: 'netease',
      name: name,
      artist: artists.map((a) => a.name).toList(),
      duration: _parseDuration(duration),
      album: album.name,
      qualities: [
        Quality(short: 'standard', bitrate: 128, format: 'mp3', size: null),
        Quality(short: 'exhigh', bitrate: 320, format: 'mp3', size: null),
        Quality(short: 'lossless', bitrate: 999, format: 'flac', size: null),
        Quality(short: 'jymaster', bitrate: 1411, format: 'flac', size: null),
      ],
      defaultQuality: Quality(short: 'jymaster', bitrate: 1411, format: 'flac', size: null),
      artPic: null,
      lyric: null,
    );
  }

  /// 解析时长字符串 "03:22" -> 202秒
  int _parseDuration(String durationStr) {
    try {
      final parts = durationStr.split(':');
      if (parts.length == 2) {
        final minutes = int.parse(parts[0]);
        final seconds = int.parse(parts[1]);
        return minutes * 60 + seconds;
      }
    } catch (e) {
      // 解析失败，返回0
    }
    return 0;
  }
}

/// 网易云艺术家
class NeteaseArtist {
  final String id;
  final String name;

  NeteaseArtist({
    required this.id,
    required this.name,
  });

  factory NeteaseArtist.fromJson(Map<String, dynamic> json) {
    return NeteaseArtist(
      id: json['id']?.toString() ?? '0',
      name: json['name']?.toString() ?? '未知艺术家',
    );
  }
}

/// 网易云专辑
class NeteaseAlbum {
  final String id;
  final String name;
  final String publishTime;

  NeteaseAlbum({
    required this.id,
    required this.name,
    required this.publishTime,
  });

  factory NeteaseAlbum.fromJson(Map<String, dynamic> json) {
    return NeteaseAlbum(
      id: json['id']?.toString() ?? '0',
      name: json['name']?.toString() ?? '未知专辑',
      publishTime: json['publishTime']?.toString() ?? '',
    );
  }
}

/// 网易云音乐播放信息
class NeteaseMusicPlayInfo {
  final String alName;
  final String arName;
  final String level;
  final String lyric;
  final String name;
  final String pic;
  final String size;
  final int status;
  final String tlyric;
  final String url;

  NeteaseMusicPlayInfo({
    required this.alName,
    required this.arName,
    required this.level,
    required this.lyric,
    required this.name,
    required this.pic,
    required this.size,
    required this.status,
    required this.tlyric,
    required this.url,
  });

  factory NeteaseMusicPlayInfo.fromJson(Map<String, dynamic> json) {
    return NeteaseMusicPlayInfo(
      alName: json['al_name']?.toString() ?? '',
      arName: json['ar_name']?.toString() ?? '',
      level: json['level']?.toString() ?? '',
      lyric: json['lyric']?.toString() ?? '',
      name: json['name']?.toString() ?? '',
      pic: json['pic']?.toString() ?? '',
      size: json['size']?.toString() ?? '',
      status: json['status'] ?? 0,
      tlyric: json['tlyric']?.toString() ?? '',
      url: json['url']?.toString() ?? '',
    );
  }

  /// 转换为PlayInfo
  PlayInfo toPlayInfo() {
    return PlayInfo(
      uri: url,
      quality: Quality(
        short: level,
        level: level,
        bitrate: _getQualityBitrate(level),
        format: _getFormatFromUrl(url),
        size: _parseSizeString(size)?.toString(),
      ),
    );
  }

  /// 根据音质获取比特率
  int _getQualityBitrate(String quality) {
    switch (quality.toLowerCase()) {
      case 'standard': case '标准音质': return 128;
      case 'exhigh': case '极高品质': return 320;
      case 'lossless': case '无损音质': return 999;
      case 'hires': case 'hi-res音质': return 1411;
      case 'jyeffect': case '高清环绕声': return 1411;
      case 'sky': case '沉浸环绕声': return 1411;
      case 'jymaster': case '超清母带': return 1411;
      default: return 320;
    }
  }

  /// 从URL获取格式
  String? _getFormatFromUrl(String url) {
    if (url.isEmpty) return null;
    
    final uri = Uri.tryParse(url);
    if (uri == null) return null;
    
    final path = uri.path.toLowerCase();
    if (path.endsWith('.mp3')) return 'mp3';
    if (path.endsWith('.flac')) return 'flac';
    if (path.endsWith('.m4a')) return 'm4a';
    
    return 'mp3'; // 默认
  }

  /// 解析大小字符串 "156.96MB" -> 字节数
  int? _parseSizeString(String sizeStr) {
    try {
      final regex = RegExp(r'([\d.]+)\s*(MB|KB|GB)', caseSensitive: false);
      final match = regex.firstMatch(sizeStr);
      
      if (match != null) {
        final value = double.parse(match.group(1)!);
        final unit = match.group(2)!.toUpperCase();
        
        switch (unit) {
          case 'KB': return (value * 1024).round();
          case 'MB': return (value * 1024 * 1024).round();
          case 'GB': return (value * 1024 * 1024 * 1024).round();
        }
      }
    } catch (e) {
      // 解析失败
    }
    return null;
  }
}
