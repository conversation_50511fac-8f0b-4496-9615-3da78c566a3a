use anyhow::{anyhow, Result};
use serde::{Deserialize, Serialize};
use serde_json::Value;
use std::collections::HashMap;
use std::sync::{Arc, Mutex};
use reqwest::header::HeaderMap;
use crate::api::utils::http_helper::send_request;
use flutter_rust_bridge::frb;
use lazy_static::lazy_static;

/// JS音乐源信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct JsMusicSourceInfo {
    pub name: String,
    pub version: String,
    pub author: String,
    pub description: String,
    pub source_code: String,
}

/// JS音乐源执行器（简化版本，暂时不支持JS执行）
pub struct JsMusicSourceExecutor {
    source_info: JsMusicSourceInfo,
}

impl JsMusicSourceExecutor {
    /// 创建新的JS音乐源执行器（简化版本）
    pub fn new(source_info: JsMusicSourceInfo) -> Result<Self> {
        // 暂时不支持JS执行，只存储源信息
        // 未来可以添加更轻量级的JS引擎支持
        Ok(Self {
            source_info,
        })
    }

    /// 搜索音乐（简化版本，返回空结果）
    pub async fn search_music(&self, _keyword: &str, _limit: u32) -> Result<Vec<HashMap<String, Value>>> {
        // 暂时返回空结果，未来可以添加JS引擎支持
        Ok(Vec::new())
    }

    /// 获取音乐播放信息（简化版本，返回None）
    pub async fn get_music_play_info(&self, _source: &str, _extra: &str) -> Result<Option<HashMap<String, Value>>> {
        // 暂时返回None，未来可以添加JS引擎支持
        Ok(None)
    }

    /// 获取歌词（简化版本，返回None）
    pub async fn get_lyric(&self, _source: &str, _extra: &str) -> Result<Option<String>> {
        // 暂时返回None，未来可以添加JS引擎支持
        Ok(None)
    }

    /// 获取源信息
    pub fn get_source_info(&self) -> &JsMusicSourceInfo {
        &self.source_info
    }
}



// 全局JS音乐源执行器
lazy_static! {
    static ref GLOBAL_JS_EXECUTOR: Arc<Mutex<Option<JsMusicSourceExecutor>>> = Arc::new(Mutex::new(None));
}

/// 创建JS音乐源执行器
#[frb]
pub fn create_js_music_source_executor(
    name: String,
    version: String,
    author: String,
    description: String,
    source_code: String,
) -> Result<bool, String> {
    let source_info = JsMusicSourceInfo {
        name,
        version,
        author,
        description,
        source_code,
    };

    match JsMusicSourceExecutor::new(source_info) {
        Ok(executor) => {
            let mut global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();
            *global_executor = Some(executor);
            Ok(true)
        }
        Err(e) => Err(format!("Failed to create JS executor: {}", e)),
    }
}

/// 销毁JS音乐源执行器
#[frb]
pub fn destroy_js_music_source_executor() -> bool {
    let mut global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();
    *global_executor = None;
    true
}

/// 检查JS音乐源执行器是否存在
#[frb]
pub fn has_js_music_source_executor() -> bool {
    let global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();
    global_executor.is_some()
}

/// 获取JS音乐源信息
#[frb]
pub fn get_js_music_source_info() -> Option<JsMusicSourceInfo> {
    let global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();
    global_executor.as_ref().map(|executor| executor.get_source_info().clone())
}

/// 搜索音乐
#[frb]
pub async fn js_search_music(keyword: String, limit: u32) -> Result<String, String> {
    let global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();

    if let Some(executor) = global_executor.as_ref() {
        match executor.search_music(&keyword, limit).await {
            Ok(results) => {
                match serde_json::to_string(&results) {
                    Ok(json_str) => Ok(json_str),
                    Err(e) => Err(format!("Failed to serialize search results: {}", e)),
                }
            }
            Err(e) => Err(format!("Search failed: {}", e)),
        }
    } else {
        Err("No JS music source executor available".to_string())
    }
}

/// 获取音乐播放信息
#[frb]
pub async fn js_get_music_play_info(source: String, extra: String) -> Result<Option<String>, String> {
    let global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();

    if let Some(executor) = global_executor.as_ref() {
        match executor.get_music_play_info(&source, &extra).await {
            Ok(Some(play_info)) => {
                match serde_json::to_string(&play_info) {
                    Ok(json_str) => Ok(Some(json_str)),
                    Err(e) => Err(format!("Failed to serialize play info: {}", e)),
                }
            }
            Ok(None) => Ok(None),
            Err(e) => Err(format!("Get play info failed: {}", e)),
        }
    } else {
        Err("No JS music source executor available".to_string())
    }
}

/// 获取歌词
#[frb]
pub async fn js_get_lyric(source: String, extra: String) -> Result<Option<String>, String> {
    let global_executor = GLOBAL_JS_EXECUTOR.lock().unwrap();

    if let Some(executor) = global_executor.as_ref() {
        match executor.get_lyric(&source, &extra).await {
            Ok(lyric) => Ok(lyric),
            Err(e) => Err(format!("Get lyric failed: {}", e)),
        }
    } else {
        Err("No JS music source executor available".to_string())
    }
}
