import 'package:flutter/cupertino.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/types/js_source_manager.dart';
import 'package:app_rhyme/types/js_music_source.dart';
import 'package:app_rhyme/utils/global_vars.dart';

class MusicSearchPage extends StatefulWidget {
  const MusicSearchPage({super.key});

  @override
  State<MusicSearchPage> createState() => _MusicSearchPageState();
}

class _MusicSearchPageState extends State<MusicSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  List<Map<String, dynamic>> _searchResults = [];
  bool _isSearching = false;
  String _searchKeyword = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _performSearch(String keyword) async {
    if (keyword.trim().isEmpty) {
      setState(() {
        _searchResults = [];
        _searchKeyword = '';
      });
      return;
    }

    final activeSource = JsSourceManager.activeSource;
    if (activeSource == null) {
      _showMessage('搜索失败', '请先设置一个活跃的音乐源', isError: true);
      return;
    }

    setState(() {
      _isSearching = true;
      _searchKeyword = keyword;
    });

    try {
      final executor = JsMusicSourceExecutor(activeSource.source);
      final results = await executor.searchMusic(keyword, limit: 20);
      
      setState(() {
        _searchResults = results;
        _isSearching = false;
      });

      globalTalker.info('[MusicSearch] 搜索完成: $keyword, 找到 ${results.length} 首歌曲');
    } catch (e) {
      setState(() {
        _isSearching = false;
        _searchResults = [];
      });
      
      globalTalker.error('[MusicSearch] 搜索失败: $e');
      _showMessage('搜索失败', '搜索时发生错误: $e', isError: true);
    }
  }

  void _showMessage(String title, String message, {bool isError = false}) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(title, style: const TextStyle().useSystemChineseFont()),
        content: Text(message, style: const TextStyle().useSystemChineseFont()),
        actions: [
          CupertinoDialogAction(
            child: Text('确定', style: const TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int milliseconds) {
    final seconds = milliseconds ~/ 1000;
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final backgroundColor = isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    
    final activeSource = JsSourceManager.activeSource;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        middle: Text(
          '搜索音乐',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 搜索栏
            Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 搜索输入框
                  CupertinoSearchTextField(
                    controller: _searchController,
                    placeholder: '搜索歌曲、歌手、专辑...',
                    style: TextStyle(color: textColor).useSystemChineseFont(),
                    onSubmitted: _performSearch,
                    onChanged: (value) {
                      if (value.trim().isEmpty) {
                        setState(() {
                          _searchResults = [];
                          _searchKeyword = '';
                        });
                      }
                    },
                  ),
                  
                  const SizedBox(height: 12),
                  
                  // 当前源信息
                  if (activeSource != null)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemBlue.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            CupertinoIcons.music_note,
                            size: 16,
                            color: CupertinoColors.systemBlue,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '当前源: ${activeSource.name}',
                            style: TextStyle(
                              color: CupertinoColors.systemBlue,
                              fontSize: 14,
                            ).useSystemChineseFont(),
                          ),
                        ],
                      ),
                    )
                  else
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                      decoration: BoxDecoration(
                        color: CupertinoColors.systemRed.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            CupertinoIcons.exclamationmark_triangle,
                            size: 16,
                            color: CupertinoColors.systemRed,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            '未设置音乐源，请先添加并启用音乐源',
                            style: TextStyle(
                              color: CupertinoColors.systemRed,
                              fontSize: 14,
                            ).useSystemChineseFont(),
                          ),
                        ],
                      ),
                    ),
                ],
              ),
            ),
            
            // 搜索结果
            Expanded(
              child: _buildSearchResults(textColor, isDarkMode),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchResults(Color textColor, bool isDarkMode) {
    if (_isSearching) {
      return const Center(
        child: CupertinoActivityIndicator(),
      );
    }

    if (_searchKeyword.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.search,
              size: 64,
              color: textColor.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              '输入关键词搜索音乐',
              style: TextStyle(
                color: textColor.withOpacity(0.6),
                fontSize: 16,
              ).useSystemChineseFont(),
            ),
          ],
        ),
      );
    }

    if (_searchResults.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              CupertinoIcons.music_note,
              size: 64,
              color: textColor.withOpacity(0.3),
            ),
            const SizedBox(height: 16),
            Text(
              '没有找到相关音乐',
              style: TextStyle(
                color: textColor.withOpacity(0.6),
                fontSize: 16,
              ).useSystemChineseFont(),
            ),
            const SizedBox(height: 8),
            Text(
              '尝试使用其他关键词',
              style: TextStyle(
                color: textColor.withOpacity(0.4),
                fontSize: 14,
              ).useSystemChineseFont(),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: _searchResults.length,
      itemBuilder: (context, index) {
        final song = _searchResults[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 8),
          decoration: BoxDecoration(
            color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
            borderRadius: BorderRadius.circular(12),
          ),
          child: CupertinoListTile(
            leading: Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: CupertinoColors.systemGrey,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                CupertinoIcons.music_note,
                color: CupertinoColors.white,
              ),
            ),
            title: Text(
              song['name'] ?? 'Unknown',
              style: TextStyle(
                color: textColor,
                fontWeight: FontWeight.w500,
              ).useSystemChineseFont(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  song['artist'] ?? 'Unknown Artist',
                  style: TextStyle(
                    color: textColor.withOpacity(0.7),
                    fontSize: 14,
                  ).useSystemChineseFont(),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  song['album'] ?? 'Unknown Album',
                  style: TextStyle(
                    color: textColor.withOpacity(0.5),
                    fontSize: 12,
                  ).useSystemChineseFont(),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (song['duration'] != null)
                  Text(
                    _formatDuration(song['duration']),
                    style: TextStyle(
                      color: textColor.withOpacity(0.5),
                      fontSize: 12,
                    ).useSystemChineseFont(),
                  ),
                const SizedBox(width: 8),
                CupertinoButton(
                  padding: EdgeInsets.zero,
                  child: Icon(
                    CupertinoIcons.play_circle,
                    color: CupertinoColors.systemBlue,
                    size: 24,
                  ),
                  onPressed: () => _playSong(song),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _playSong(Map<String, dynamic> song) {
    // 这里应该调用音乐播放器播放歌曲
    globalTalker.info('[MusicSearch] 播放歌曲: ${song['name']}');
    
    _showMessage(
      '播放音乐',
      '正在播放: ${song['name']}\n歌手: ${song['artist']}\n\n注意：这是演示版本，实际播放功能需要完整的音频播放器支持。',
    );
  }
}
