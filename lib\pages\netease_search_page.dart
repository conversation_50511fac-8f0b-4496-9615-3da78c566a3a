import 'package:app_rhyme/types/netease_music_container.dart';
import 'package:app_rhyme/utils/colors.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:infinite_scroll_pagination/infinite_scroll_pagination.dart';

/// 网易云音乐搜索页面
class NeteaseSearchPage extends StatefulWidget {
  const NeteaseSearchPage({super.key});

  @override
  State<NeteaseSearchPage> createState() => _NeteaseSearchPageState();
}

class _NeteaseSearchPageState extends State<NeteaseSearchPage> {
  final PagingController<int, NeteaseMusicContainer> _pagingController =
      PagingController(firstPageKey: 1);
  final TextEditingController _searchController = TextEditingController();
  
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _pagingController.addPageRequestListener((pageKey) {
      _fetchMusic(pageKey);
    });
  }

  @override
  void dispose() {
    _pagingController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _fetchMusic(int pageKey) async {
    if (_searchController.text.trim().isEmpty) {
      _pagingController.appendLastPage([]);
      return;
    }

    try {
      setState(() => _isLoading = true);
      
      final containers = await NeteaseMusicSearchService.searchMusic(
        name: _searchController.text.trim(),
        limit: 30,
      );

      if (containers.isEmpty) {
        _pagingController.appendLastPage([]);
      } else {
        // 网易云API不支持分页，所以直接返回所有结果
        _pagingController.appendLastPage(containers);
      }
    } catch (error) {
      _pagingController.error = error;
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _onSearchSubmitted() {
    _pagingController.refresh();
  }

  @override
  Widget build(BuildContext context) {
    final bool isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final Color backgroundColor = isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final Color textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        middle: Text(
          '网易云音乐搜索',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 搜索框
            Container(
              padding: const EdgeInsets.all(16),
              child: CupertinoSearchTextField(
                controller: _searchController,
                placeholder: '搜索歌曲名称',
                style: TextStyle(color: textColor).useSystemChineseFont(),
                onSubmitted: (_) => _onSearchSubmitted(),
                onSuffixTap: () {
                  _searchController.clear();
                  _pagingController.refresh();
                },
              ),
            ),
            
            // 搜索按钮
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              width: double.infinity,
              child: CupertinoButton.filled(
                onPressed: _isLoading ? null : _onSearchSubmitted,
                child: _isLoading
                    ? const CupertinoActivityIndicator(color: CupertinoColors.white)
                    : Text(
                        '搜索',
                        style: const TextStyle(color: CupertinoColors.white).useSystemChineseFont(),
                      ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // 搜索结果列表
            Expanded(
              child: PagedListView<int, NeteaseMusicContainer>(
                pagingController: _pagingController,
                builderDelegate: PagedChildBuilderDelegate<NeteaseMusicContainer>(
                  itemBuilder: (context, container, index) => 
                      _buildMusicItem(context, container, isDarkMode),
                  firstPageErrorIndicatorBuilder: (context) => _buildErrorWidget(),
                  newPageErrorIndicatorBuilder: (context) => _buildErrorWidget(),
                  firstPageProgressIndicatorBuilder: (context) => 
                      const Center(child: CupertinoActivityIndicator()),
                  newPageProgressIndicatorBuilder: (context) => 
                      const Center(child: CupertinoActivityIndicator()),
                  noItemsFoundIndicatorBuilder: (context) => _buildEmptyWidget(),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMusicItem(BuildContext context, NeteaseMusicContainer container, bool isDarkMode) {
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    final subtitleColor = isDarkMode ? CupertinoColors.systemGrey : CupertinoColors.systemGrey2;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: CupertinoListTile(
        title: Text(
          container.info.name,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ).useSystemChineseFont(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              container.info.artist.join(', '),
              style: TextStyle(
                color: subtitleColor,
                fontSize: 14,
              ).useSystemChineseFont(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            if (container.info.album != null) ...[
              const SizedBox(height: 2),
              Text(
                container.info.album!,
                style: TextStyle(
                  color: subtitleColor,
                  fontSize: 12,
                ).useSystemChineseFont(),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 时长显示
            if (container.info.duration != null)
              Text(
                _formatDuration(container.info.duration!),
                style: TextStyle(
                  color: subtitleColor,
                  fontSize: 12,
                ).useSystemChineseFont(),
              ),
            const SizedBox(width: 8),
            // 播放按钮
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 32,
              onPressed: () => _playMusic(container),
              child: Icon(
                CupertinoIcons.play_circle,
                color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
                size: 28,
              ),
            ),
          ],
        ),
        onTap: () => _showMusicDetails(context, container),
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.exclamationmark_triangle,
            size: 64,
            color: CupertinoColors.systemRed,
          ),
          const SizedBox(height: 16),
          Text(
            '搜索失败',
            style: const TextStyle(fontSize: 18).useSystemChineseFont(),
          ),
          const SizedBox(height: 8),
          CupertinoButton(
            onPressed: () => _pagingController.refresh(),
            child: Text(
              '重试',
              style: const TextStyle().useSystemChineseFont(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.music_note,
            size: 64,
            color: CupertinoColors.systemGrey,
          ),
          const SizedBox(height: 16),
          Text(
            _searchController.text.isEmpty ? '输入歌曲名称开始搜索' : '未找到相关歌曲',
            style: const TextStyle(
              fontSize: 16,
              color: CupertinoColors.systemGrey,
            ).useSystemChineseFont(),
          ),
        ],
      ),
    );
  }

  void _playMusic(NeteaseMusicContainer container) async {
    try {
      LogToast.info('开始播放', '正在获取 ${container.info.name} 的播放链接...', 
          '[NeteaseSearchPage] Starting to play music');
      
      final success = await container.updateAll();
      if (success && container.playInfo != null) {
        LogToast.success('播放成功', '开始播放: ${container.info.name}', 
            '[NeteaseSearchPage] Successfully started playing');
        // 这里可以集成到现有的音频播放系统
      } else {
        LogToast.error('播放失败', '无法获取 ${container.info.name} 的播放链接', 
            '[NeteaseSearchPage] Failed to get play info');
      }
    } catch (e) {
      LogToast.error('播放失败', '播放 ${container.info.name} 时出错: $e', 
          '[NeteaseSearchPage] Error playing music: $e');
    }
  }

  void _showMusicDetails(BuildContext context, NeteaseMusicContainer container) {
    showCupertinoDialog(
      context: context,
      builder: (context) => CupertinoAlertDialog(
        title: Text(
          container.info.name,
          style: const TextStyle().useSystemChineseFont(),
        ),
        content: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('艺术家: ${container.info.artist.join(', ')}'),
            if (container.info.album != null)
              Text('专辑: ${container.info.album}'),
            if (container.info.duration != null)
              Text('时长: ${_formatDuration(container.info.duration!)}'),
          ],
        ),
        actions: [
          CupertinoDialogAction(
            child: Text('取消', style: const TextStyle().useSystemChineseFont()),
            onPressed: () => Navigator.of(context).pop(),
          ),
          CupertinoDialogAction(
            child: Text('播放', style: const TextStyle().useSystemChineseFont()),
            onPressed: () {
              Navigator.of(context).pop();
              _playMusic(container);
            },
          ),
        ],
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}
