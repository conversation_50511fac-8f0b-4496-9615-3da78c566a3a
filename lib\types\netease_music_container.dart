import 'dart:convert';
import 'package:app_rhyme/models/netease_music.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:audio_service/audio_service.dart';
import 'package:get/get.dart';
import 'package:http/http.dart' as http;
import 'package:just_audio/just_audio.dart';

/// 网易云音乐容器，用于管理单首歌曲的播放信息
class NeteaseMusicContainer {
  late NeteaseMusicData musicData;
  late MusicInfo info;
  late Rx<Quality?> currentQuality;
  PlayInfo? playInfo;
  late AudioSource audioSource;
  DateTime lastUpdate = DateTime(1999);

  static const String _baseUrl = 'https://api.kxzjoker.cn/api';
  static const String _musicEndpoint = '/163_music';

  NeteaseMusicContainer(this.musicData) {
    info = musicData.toMusicInfo();
    currentQuality = Rx<Quality?>(info.defaultQuality);
    audioSource = AudioSource.asset("assets/blank.mp3", tag: _toMediaItem());
  }

  /// 使上次更新时间过期
  void setOutdate() {
    lastUpdate = DateTime(1999);
  }

  /// 更新所有信息（播放链接、歌词等）
  Future<bool> updateAll([Quality? quality]) async {
    try {
      final playInfo = await getCurrentMusicPlayInfo(quality);
      if (playInfo != null) {
        await _updateAudioSource();
        return true;
      }
      return false;
    } catch (e) {
      globalTalker.error('[NeteaseMusicContainer] 更新失败: $e');
      return false;
    }
  }

  /// 获取当前音乐的播放信息
  Future<PlayInfo?> getCurrentMusicPlayInfo([Quality? quality]) async {
    // 如果已经有播放信息且未过期，直接返回
    if (playInfo != null && !_isOutdated()) {
      return playInfo;
    }

    try {
      final qualityLevel = quality?.short ?? currentQuality.value?.short ?? 'jymaster';
      
      globalTalker.info('[NeteaseMusicContainer] 获取播放信息: ${info.name}');
      
      final url = Uri.parse('$_baseUrl$_musicEndpoint');
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'ids': info.id.toString(),
          'level': qualityLevel,
          'type': 'json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);
      
      if (data['status'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      // 创建播放信息
      final neteasePlayInfo = NeteaseMusicPlayInfo.fromJson(data);
      playInfo = neteasePlayInfo.toPlayInfo();
      
      // 更新歌词
      if (neteasePlayInfo.lyric.isNotEmpty) {
        info.lyric = neteasePlayInfo.lyric;
      }
      
      // 更新封面 - MusicInfo的artPic是final的，不能修改
      // if (neteasePlayInfo.pic.isNotEmpty) {
      //   info.artPic = neteasePlayInfo.pic;
      // }

      currentQuality.value = playInfo!.quality;
      lastUpdate = DateTime.now();
      
      globalTalker.info('[NeteaseMusicContainer] 获取播放信息成功: ${info.name}');
      return playInfo;
      
    } catch (e) {
      globalTalker.error('[NeteaseMusicContainer] 获取播放信息失败: $e');
      LogToast.error('获取播放信息失败', '无法获取 ${info.name} 的播放链接: $e', 
          '[NeteaseMusicContainer] Failed to get play info: $e');
      return null;
    }
  }

  /// 更新音频源
  Future<void> _updateAudioSource() async {
    if (playInfo?.uri.isNotEmpty == true) {
      audioSource = AudioSource.uri(
        Uri.parse(playInfo!.uri),
        tag: _toMediaItem(),
      );
    }
  }

  /// 将音乐信息转化为MediaItem
  MediaItem _toMediaItem() {
    Uri? artUri;
    if (info.artPic != null && info.artPic!.isNotEmpty) {
      artUri = Uri.parse(info.artPic!);
    }
    
    return MediaItem(
      id: info.id.toString(),
      title: info.name,
      album: info.album,
      artUri: artUri,
      artist: info.artist.join(", "),
      duration: info.duration != null ? Duration(seconds: info.duration!) : null,
    );
  }

  /// 检查是否过期（5分钟）
  bool _isOutdated() {
    return DateTime.now().difference(lastUpdate).inMinutes > 5;
  }

  /// 获取歌词
  Future<String> fetchLyric() async {
    if (info.lyric != null && info.lyric!.isNotEmpty) {
      return info.lyric!;
    }
    
    // 如果没有歌词，尝试获取播放信息（包含歌词）
    final playInfo = await getCurrentMusicPlayInfo();
    if (playInfo != null && info.lyric != null && info.lyric!.isNotEmpty) {
      return info.lyric!;
    }
    
    return '[00:00.00]暂无歌词';
  }

  /// 切换音质
  Future<bool> changeQuality(Quality quality) async {
    try {
      currentQuality.value = quality;
      setOutdate(); // 使缓存过期
      final playInfo = await getCurrentMusicPlayInfo(quality);
      if (playInfo != null) {
        await _updateAudioSource();
        return true;
      }
      return false;
    } catch (e) {
      globalTalker.error('[NeteaseMusicContainer] 切换音质失败: $e');
      return false;
    }
  }

  @override
  String toString() {
    return 'NeteaseMusicContainer(${info.name} - ${info.artist.join(", ")})';
  }
}

/// 网易云音乐搜索服务
class NeteaseMusicSearchService {
  static const String _baseUrl = 'https://api.kxzjoker.cn/api';
  static const String _searchEndpoint = '/163_search';

  /// 搜索音乐
  static Future<List<NeteaseMusicContainer>> searchMusic({
    required String name,
    int limit = 10,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl$_searchEndpoint')
          .replace(queryParameters: {
        'name': name,
        'limit': limit.toString(),
      });

      globalTalker.info('[NeteaseMusicSearchService] 搜索音乐: $name');
      
      final response = await http.get(url);
      
      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);
      
      if (data['code'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      final List<dynamic> musicList = data['data'] ?? [];
      final List<NeteaseMusicContainer> containers = [];

      for (final musicJson in musicList) {
        try {
          final musicData = NeteaseMusicData.fromJson(musicJson);
          final container = NeteaseMusicContainer(musicData);
          containers.add(container);
        } catch (e) {
          globalTalker.warning('[NeteaseMusicSearchService] 跳过无效音乐数据: $e');
        }
      }

      globalTalker.info('[NeteaseMusicSearchService] 搜索完成，找到 ${containers.length} 首歌曲');
      return containers;
      
    } catch (e) {
      globalTalker.error('[NeteaseMusicSearchService] 搜索失败: $e');
      LogToast.error('搜索失败', '网易云音乐搜索失败: $e', '[NeteaseMusicSearchService] Search failed: $e');
      return [];
    }
  }
}
