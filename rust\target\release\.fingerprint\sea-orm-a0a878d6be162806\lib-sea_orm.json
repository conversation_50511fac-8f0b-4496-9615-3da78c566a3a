{"rustc": 1842507548689473721, "features": "[\"macros\", \"postgres-array\", \"runtime-tokio\", \"runtime-tokio-rustls\", \"sea-query-binder\", \"serde_json\", \"sqlx\", \"sqlx-all\", \"sqlx-dep\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"with-json\"]", "declared_features": "[\"bigdecimal\", \"chrono\", \"debug-print\", \"default\", \"ipnetwork\", \"json-array\", \"macros\", \"mock\", \"pgvector\", \"postgres-array\", \"postgres-vector\", \"proxy\", \"runtime-actix\", \"runtime-actix-native-tls\", \"runtime-actix-rustls\", \"runtime-async-std\", \"runtime-async-std-native-tls\", \"runtime-async-std-rustls\", \"runtime-tokio\", \"runtime-tokio-native-tls\", \"runtime-tokio-rustls\", \"rust_decimal\", \"sea-orm-internal\", \"sea-query-binder\", \"seaography\", \"serde_json\", \"sqlite-use-returning-for-3_35\", \"sqlx\", \"sqlx-all\", \"sqlx-dep\", \"sqlx-mysql\", \"sqlx-postgres\", \"sqlx-sqlite\", \"tests-cfg\", \"time\", \"uuid\", \"with-bigdecimal\", \"with-chrono\", \"with-ipnetwork\", \"with-json\", \"with-rust_decimal\", \"with-time\", \"with-uuid\"]", "target": 5911612443439219948, "profile": 16503403049695105087, "path": 15818151992024647854, "deps": [[1188017320647144970, "async_stream", false, 18443257069536119074], [3150220818285335163, "url", false, 2639943738301166377], [3302295501534065768, "strum", false, 7348362572171020743], [3525657182790186941, "ouroboros", false, 15430118992165329055], [5986029879202738730, "log", false, 17276290037950454565], [6841140121864026414, "sqlx", false, 6656091416863686224], [7817431159498251116, "sea_query_binder", false, 14400769334879851569], [8569119365930580996, "serde_json", false, 5018714320819918896], [8606274917505247608, "tracing", false, 17630907087731180649], [9689903380558560274, "serde", false, 12520861906457967598], [10629569228670356391, "futures_util", false, 1146063152774682336], [10806645703491011684, "thiserror", false, 1865417846324893241], [11946729385090170470, "async_trait", false, 18365533750232480685], [13832445978929702944, "sea_query", false, 11855249909114252999], [17625815326946657219, "sea_orm_macros", false, 18302692557688473940]], "local": [{"CheckDepInfo": {"dep_info": "release\\.fingerprint\\sea-orm-a0a878d6be162806\\dep-lib-sea_orm", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}