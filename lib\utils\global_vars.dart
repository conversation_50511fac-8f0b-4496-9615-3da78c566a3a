import 'dart:io';
import 'package:app_rhyme/audioControl/audio_controller.dart';
import 'package:app_rhyme/src/rust/api/init.dart';
import 'package:app_rhyme/src/rust/api/types/config.dart';
import 'package:app_rhyme/types/chore.dart';

import 'package:app_rhyme/types/js_music_source.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:talker/talker.dart';
import 'package:flutter/foundation.dart';

// 配置Talker以减少日志输出
Talker globalTalker = Talker(
  settings: TalkerSettings(
    // 在发布模式下禁用控制台日志
    enabled: true,
    useConsoleLogs: kDebugMode, // 只在调试模式下输出到控制台
  ),
);

// 在`init_backend.dart` 中被初始化
late Config globalConfig;

// ExternApiEvaler已移除以减小体积
late JsMusicSourceExecutor? globalJsMusicSourceExecutor;
late AudioHandler globalAudioHandler;
late AudioUiController globalAudioUiController;
late PackageInfo globalPackageInfo;
late String globalDocumentPath;
late Connectivity globalConnectivity;
ConnectivityStateSimple globalConnectivityStateSimple =
    ConnectivityStateSimple.none;

// 异步加载JS音乐源，避免阻塞应用启动
Future<void> _loadJsMusicSourceAsync() async {
  try {
    // 延迟500ms再开始加载，确保应用界面已经显示
    await Future.delayed(const Duration(milliseconds: 500));

    final jsSourceFile = File('$globalDocumentPath/AppRhyme/config/js_music_source_url.txt');
    if (await jsSourceFile.exists()) {
      final url = await jsSourceFile.readAsString();
      if (url.isNotEmpty) {
        globalTalker.info("[_loadJsMusicSourceAsync] 开始异步加载JS音乐源...");
        final source = await JsMusicSource.fromUrl(url);
        globalJsMusicSourceExecutor = JsMusicSourceExecutor(source);
        globalTalker.info("[_loadJsMusicSourceAsync] JS音乐源加载完成: ${source.name}");
      }
    }
  } catch (e) {
    globalTalker.warning("[_loadJsMusicSourceAsync] 异步加载JS音乐源失败: $e");
  }
}

// 初始化全局变量
// 即可用于在App启动时也可用于配置更新时
Future<void> initGlobalVars() async {
  // 初始化rust全局配置，将documentPath设置为应用程序文档目录
  globalDocumentPath = (await getApplicationDocumentsDirectory()).path;
  // 初始化全局变量globalConfig
  globalConfig = await initBackend(storeRoot: globalDocumentPath);
  // ExternApiEvaler已移除以减小体积

  // 初始化JS音乐源执行器，异步加载避免阻塞启动
  globalJsMusicSourceExecutor = null;
  // 延迟加载JS音乐源，避免阻塞应用启动
  _loadJsMusicSourceAsync();
  // 初始化应用包信息，添加错误处理
  try {
    globalPackageInfo = await PackageInfo.fromPlatform();
  } catch (e) {
    globalTalker.error("[initGlobalVars] 获取包信息失败: $e");
    // 创建默认的包信息以防止应用崩溃
    globalPackageInfo = PackageInfo(
      appName: 'AppRhyme',
      packageName: 'canxin.app.rhyme',
      version: '1.0.9',
      buildNumber: '1',
      buildSignature: '',
      installerStore: null,
    );
  }
  // 监听网络状态变化
  globalConnectivity = Connectivity();
  globalConnectivity.onConnectivityChanged
      .listen((List<ConnectivityResult> connectivityResult) {
    if (connectivityResult.contains(ConnectivityResult.wifi)) {
      globalConnectivityStateSimple = ConnectivityStateSimple.wifi;
    } else if (connectivityResult.contains(ConnectivityResult.mobile)) {
      globalConnectivityStateSimple = ConnectivityStateSimple.mobile;
    } else {
      globalConnectivityStateSimple = ConnectivityStateSimple.none;
    }
  });
}
