import 'package:app_rhyme/desktop/home.dart';
import 'package:app_rhyme/mobile/home.dart';
import 'package:app_rhyme/utils/mobile_device.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:app_rhyme/audioControl/audio_controller.dart';
import 'package:app_rhyme/src/rust/frb_generated.dart';
import 'package:app_rhyme/utils/bypass_netimg_error.dart';
import 'package:app_rhyme/utils/desktop_device.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/favorites_helper.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';


// 内存清理函数
void _scheduleMemoryCleanup() {
  // 清理图像缓存
  PaintingBinding.instance.imageCache.clear();
  PaintingBinding.instance.imageCache.clearLiveImages();

  // 更频繁的定期清理
  Future.delayed(const Duration(minutes: 3), () {
    _scheduleMemoryCleanup();
  });
}

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 性能优化：预热Flutter引擎
  WidgetsBinding.instance.addPostFrameCallback((_) {
    // 大幅减少图像缓存设置以避免GPU内存溢出
    PaintingBinding.instance.imageCache.maximumSize = 30; // 极大减少缓存数量
    PaintingBinding.instance.imageCache.maximumSizeBytes = 3 << 20; // 3MB，极大减少内存占用

    // 启用图像缓存清理
    PaintingBinding.instance.imageCache.clearLiveImages();

    // 预热键盘系统 - 延迟执行避免阻塞启动
    Future.delayed(const Duration(milliseconds: 300), () {
      try {
        // 预热文本输入系统
        SystemChannels.textInput.invokeMethod('TextInput.hide');

        // 预热平台视图 - 移除有问题的SystemUIMode调用
        // SystemChannels.platform.invokeMethod('SystemChrome.setEnabledSystemUIMode');

      } catch (e) {
        // 静默处理预热错误
      }
    });

    // 更频繁的内存清理
    Future.delayed(const Duration(minutes: 2), () {
      _scheduleMemoryCleanup();
    });
  });

  await RustLib.init();
  await initGlobalVars();

  // 必须先初始化音频组件，否则UI会出错
  await initGlobalAudioHandler();
  await initGlobalAudioUiController();

  // 立即启动应用
  runApp(const MyApp());

  // 异步初始化其他非关键组件
  WidgetsBinding.instance.addPostFrameCallback((_) async {
    try {
      await initBypassNetImgError();

      // 初始化"我的喜欢"歌单
      await FavoritesHelper.initializeFavoritesPlaylist();

      await initDesktopWindowSetting();

      globalTalker.info("[main] 所有组件初始化完成");
    } catch (e) {
      globalTalker.error("[main] 延迟初始化失败: $e");
    }
  });
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await initMobileDevice(context);
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // 实时检测屏幕方向，横屏时切换到PC端界面
        final bool isLandscape = constraints.maxWidth > constraints.maxHeight;

        return CupertinoApp(
          debugShowCheckedModeBanner: false, // 去除右上角的DEBUG图标
          localizationsDelegates: const [
            DefaultMaterialLocalizations.delegate,
            DefaultCupertinoLocalizations.delegate,
            DefaultWidgetsLocalizations.delegate,
          ],
          theme: CupertinoThemeData(
            applyThemeToAll: true,
            textTheme: CupertinoTextThemeData(
              textStyle: const TextStyle(color: CupertinoColors.black)
                  .useSystemChineseFont(),
            ),
          ),
          // 横屏时使用PC端界面，竖屏时使用手机端界面
          home: isLandscape
              ? const DesktopHome()
              : const MobileHome(),
        );
      },
    );
  }
}
