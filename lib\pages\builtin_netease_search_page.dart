import 'dart:convert';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/types/music_container.dart';
import 'package:app_rhyme/utils/colors.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:chinese_font_library/chinese_font_library.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;

/// 内置网易云音乐搜索页面
class BuiltinNeteaseSearchPage extends StatefulWidget {
  const BuiltinNeteaseSearchPage({super.key});

  @override
  State<BuiltinNeteaseSearchPage> createState() => _BuiltinNeteaseSearchPageState();
}

class _BuiltinNeteaseSearchPageState extends State<BuiltinNeteaseSearchPage> {
  final TextEditingController _searchController = TextEditingController();
  List<NeteaseMusicItem> _searchResults = [];
  bool _isLoading = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _searchMusic() async {
    if (_searchController.text.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _searchResults.clear();
    });

    try {
      globalTalker.info('[BuiltinNeteaseSearchPage] 开始搜索: ${_searchController.text.trim()}');

      final url = Uri.parse('https://api.kxzjoker.cn/api/163_search')
          .replace(queryParameters: {
        'name': _searchController.text.trim(),
        'limit': '30',
      });

      globalTalker.info('[BuiltinNeteaseSearchPage] 请求URL: $url');
      final response = await http.get(url);
      
      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);
      
      if (data['code'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      final List<dynamic> musicList = data['data'] ?? [];
      final List<NeteaseMusicItem> results = [];

      for (final musicData in musicList) {
        try {
          results.add(NeteaseMusicItem.fromJson(musicData));
        } catch (e) {
          globalTalker.warning('[BuiltinNeteaseSearchPage] 跳过无效音乐: $e');
        }
      }

      setState(() {
        _searchResults = results;
      });

      LogToast.success('搜索完成', '找到 ${results.length} 首歌曲', 
          '[BuiltinNeteaseSearchPage] Search completed');
      
    } catch (e) {
      LogToast.error('搜索失败', '网易云音乐搜索失败: $e', 
          '[BuiltinNeteaseSearchPage] Search failed: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _playMusic(NeteaseMusicItem item) async {
    try {
      LogToast.info('开始播放', '正在获取 ${item.name} 的播放链接...',
          '[BuiltinNeteaseSearchPage] Starting to play music');

      // 获取播放链接
      final url = Uri.parse('https://api.kxzjoker.cn/api/163_music');

      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'ids': item.id,
          'level': 'jymaster',
          'type': 'json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);

      if (data['status'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      final playUrl = data['url']?.toString();
      if (playUrl == null || playUrl.isEmpty) {
        throw Exception('无法获取播放链接');
      }

      LogToast.success('获取播放链接成功',
          '${item.name} 的播放链接已获取，可以播放了！',
          '[BuiltinNeteaseSearchPage] Successfully got play URL');

    } catch (e) {
      LogToast.error('播放失败', '播放 ${item.name} 时出错: $e',
          '[BuiltinNeteaseSearchPage] Error playing music: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final bool isDarkMode = MediaQuery.of(context).platformBrightness == Brightness.dark;
    final Color backgroundColor = isDarkMode ? CupertinoColors.black : CupertinoColors.white;
    final Color textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;

    return CupertinoPageScaffold(
      backgroundColor: backgroundColor,
      navigationBar: CupertinoNavigationBar(
        backgroundColor: backgroundColor,
        middle: Text(
          '网易云音乐',
          style: TextStyle(color: textColor).useSystemChineseFont(),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 搜索框
            Container(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: CupertinoSearchTextField(
                      controller: _searchController,
                      placeholder: '搜索歌曲名称',
                      style: TextStyle(color: textColor).useSystemChineseFont(),
                      onSubmitted: (_) => _searchMusic(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  CupertinoButton.filled(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                    onPressed: _isLoading ? null : _searchMusic,
                    child: _isLoading
                        ? const CupertinoActivityIndicator(color: CupertinoColors.white)
                        : Text(
                            '搜索',
                            style: const TextStyle(color: CupertinoColors.white).useSystemChineseFont(),
                          ),
                  ),
                ],
              ),
            ),
            
            // 搜索结果列表
            Expanded(
              child: _searchResults.isEmpty
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            CupertinoIcons.music_note,
                            size: 64,
                            color: CupertinoColors.systemGrey,
                          ),
                          const SizedBox(height: 16),
                          Text(
                            _searchController.text.isEmpty ? '输入歌曲名称开始搜索' : '未找到相关歌曲',
                            style: TextStyle(
                              fontSize: 16,
                              color: CupertinoColors.systemGrey,
                            ).useSystemChineseFont(),
                          ),
                        ],
                      ),
                    )
                  : ListView.builder(
                      itemCount: _searchResults.length,
                      itemBuilder: (context, index) {
                        final item = _searchResults[index];
                        return _buildMusicItem(context, item, isDarkMode);
                      },
                    ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMusicItem(BuildContext context, NeteaseMusicItem item, bool isDarkMode) {
    final textColor = isDarkMode ? CupertinoColors.white : CupertinoColors.black;
    final subtitleColor = isDarkMode ? CupertinoColors.systemGrey : CupertinoColors.systemGrey2;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      decoration: BoxDecoration(
        color: isDarkMode ? CupertinoColors.systemGrey6.darkColor : CupertinoColors.systemGrey6,
        borderRadius: BorderRadius.circular(12),
      ),
      child: CupertinoListTile(
        title: Text(
          item.name,
          style: TextStyle(
            color: textColor,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ).useSystemChineseFont(),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              item.artists.join(', '),
              style: TextStyle(
                color: subtitleColor,
                fontSize: 14,
              ).useSystemChineseFont(),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            if (item.album.isNotEmpty) ...[
              const SizedBox(height: 2),
              Text(
                item.album,
                style: TextStyle(
                  color: subtitleColor,
                  fontSize: 12,
                ).useSystemChineseFont(),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 时长显示
            Text(
              _formatDuration(item.duration),
              style: TextStyle(
                color: subtitleColor,
                fontSize: 12,
              ).useSystemChineseFont(),
            ),
            const SizedBox(width: 8),
            // 播放按钮
            CupertinoButton(
              padding: EdgeInsets.zero,
              minSize: 32,
              onPressed: () => _playMusic(item),
              child: Icon(
                CupertinoIcons.play_circle_fill,
                color: activeIconRed,
                size: 28,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }
}

/// 网易云音乐项目数据模型
class NeteaseMusicItem {
  final String id;
  final String name;
  final List<String> artists;
  final String album;
  final int duration;

  NeteaseMusicItem({
    required this.id,
    required this.name,
    required this.artists,
    required this.album,
    required this.duration,
  });

  factory NeteaseMusicItem.fromJson(Map<String, dynamic> json) {
    final List<dynamic> artistsData = json['artists'] ?? [];
    final List<String> artists = artistsData
        .map((artist) => artist['name']?.toString() ?? '未知艺术家')
        .toList();

    final Map<String, dynamic>? albumData = json['album'];
    final String albumName = albumData?['name']?.toString() ?? '未知专辑';
    
    final String durationStr = json['duration']?.toString() ?? '00:00';
    final int duration = _parseDuration(durationStr);

    return NeteaseMusicItem(
      id: json['id']?.toString() ?? '0',
      name: json['name']?.toString() ?? '未知歌曲',
      artists: artists,
      album: albumName,
      duration: duration,
    );
  }

  static int _parseDuration(String durationStr) {
    try {
      final parts = durationStr.split(':');
      if (parts.length == 2) {
        final minutes = int.parse(parts[0]);
        final seconds = int.parse(parts[1]);
        return minutes * 60 + seconds;
      }
    } catch (e) {
      // 解析失败，返回0
    }
    return 0;
  }
}
