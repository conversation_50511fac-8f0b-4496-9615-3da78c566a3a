import 'dart:convert';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:http/http.dart' as http;

/// 网易云音乐API服务
class NeteaseApiService {
  static const String _baseUrl = 'https://api.kxzjoker.cn/api';
  static const String _searchEndpoint = '/163_search';
  static const String _musicEndpoint = '/163_music';

  /// 搜索音乐
  static Future<List<MusicAggregatorW>> searchMusic({
    required String name,
    int limit = 10,
  }) async {
    try {
      final url = Uri.parse('$_baseUrl$_searchEndpoint')
          .replace(queryParameters: {
        'name': name,
        'limit': limit.toString(),
      });

      globalTalker.info('[NeteaseApiService] 搜索音乐: $name');
      
      final response = await http.get(url);
      
      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);
      
      if (data['code'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      final List<dynamic> musicList = data['data'] ?? [];
      final List<MusicAggregatorW> aggregators = [];

      for (final musicData in musicList) {
        try {
          final aggregator = await _createMusicAggregator(musicData);
          aggregators.add(aggregator);
        } catch (e) {
          globalTalker.warning('[NeteaseApiService] 跳过无效音乐数据: $e');
        }
      }

      globalTalker.info('[NeteaseApiService] 搜索完成，找到 ${aggregators.length} 首歌曲');
      return aggregators;
      
    } catch (e) {
      globalTalker.error('[NeteaseApiService] 搜索失败: $e');
      LogToast.error('搜索失败', '网易云音乐搜索失败: $e', '[NeteaseApiService] Search failed: $e');
      return [];
    }
  }

  /// 获取音乐播放信息
  static Future<PlayInfo?> getMusicPlayInfo({
    required String musicId,
    String quality = 'jymaster', // 默认超清母带
  }) async {
    try {
      final url = Uri.parse('$_baseUrl$_musicEndpoint');
      
      globalTalker.info('[NeteaseApiService] 获取播放信息: $musicId');
      
      final response = await http.post(
        url,
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        body: {
          'ids': musicId,
          'level': quality,
          'type': 'json',
        },
      );

      if (response.statusCode != 200) {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }

      final data = json.decode(response.body);
      
      if (data['status'] != 200) {
        throw Exception('API错误: ${data['message'] ?? '未知错误'}');
      }

      // 创建PlayInfo对象
      final playInfo = PlayInfo(
        uri: data['url'] ?? '',
        quality: Quality(
          summary: quality,
          bitrate: _getQualityBitrate(quality),
          format: _getFormatFromUrl(data['url'] ?? ''),
          size: _parseSizeString(data['size'] ?? ''),
        ),
      );

      globalTalker.info('[NeteaseApiService] 获取播放信息成功: ${data['name']}');
      return playInfo;
      
    } catch (e) {
      globalTalker.error('[NeteaseApiService] 获取播放信息失败: $e');
      return null;
    }
  }

  /// 创建MusicAggregator
  static Future<MusicAggregatorW> _createMusicAggregator(Map<String, dynamic> musicData) async {
    // 解析艺术家信息
    final List<dynamic> artistsData = musicData['artists'] ?? [];
    final List<String> artists = artistsData
        .map((artist) => artist['name']?.toString() ?? '未知艺术家')
        .toList();

    // 解析专辑信息
    final Map<String, dynamic>? albumData = musicData['album'];
    final String albumName = albumData?['name']?.toString() ?? '未知专辑';

    // 解析时长 (格式: "03:22" -> 秒数)
    final String durationStr = musicData['duration']?.toString() ?? '00:00';
    final int duration = _parseDuration(durationStr);

    // 创建MusicInfo
    final musicInfo = MusicInfo(
      id: PlatformInt64(musicData['id']?.toString() ?? '0'),
      source: 'netease',
      name: musicData['name']?.toString() ?? '未知歌曲',
      artist: artists,
      duration: duration,
      album: albumName,
      qualities: [
        Quality(summary: 'standard', bitrate: 128, format: 'mp3', size: null),
        Quality(summary: 'exhigh', bitrate: 320, format: 'mp3', size: null),
        Quality(summary: 'lossless', bitrate: 999, format: 'flac', size: null),
        Quality(summary: 'jymaster', bitrate: 1411, format: 'flac', size: null),
      ],
      defaultQuality: Quality(summary: 'jymaster', bitrate: 1411, format: 'flac', size: null),
      artPic: null, // 网易云的封面需要单独获取
      lyric: null,  // 歌词需要单独获取
    );

    // 这里需要创建一个MusicW实例，但由于它是Rust绑定的，我们需要通过其他方式
    // 暂时返回一个占位符，实际实现需要调用Rust代码
    throw UnimplementedError('需要实现MusicAggregatorW的创建');
  }

  /// 解析时长字符串 "03:22" -> 202秒
  static int _parseDuration(String durationStr) {
    try {
      final parts = durationStr.split(':');
      if (parts.length == 2) {
        final minutes = int.parse(parts[0]);
        final seconds = int.parse(parts[1]);
        return minutes * 60 + seconds;
      }
    } catch (e) {
      // 解析失败，返回0
    }
    return 0;
  }

  /// 根据音质获取比特率
  static int _getQualityBitrate(String quality) {
    switch (quality) {
      case 'standard': return 128;
      case 'exhigh': return 320;
      case 'lossless': return 999;
      case 'hires': return 1411;
      case 'jyeffect': return 1411;
      case 'sky': return 1411;
      case 'jymaster': return 1411;
      default: return 320;
    }
  }

  /// 从URL获取格式
  static String? _getFormatFromUrl(String url) {
    if (url.isEmpty) return null;
    
    final uri = Uri.tryParse(url);
    if (uri == null) return null;
    
    final path = uri.path.toLowerCase();
    if (path.endsWith('.mp3')) return 'mp3';
    if (path.endsWith('.flac')) return 'flac';
    if (path.endsWith('.m4a')) return 'm4a';
    
    return 'mp3'; // 默认
  }

  /// 解析大小字符串 "156.96MB" -> 字节数
  static int? _parseSizeString(String sizeStr) {
    try {
      final regex = RegExp(r'([\d.]+)\s*(MB|KB|GB)', caseSensitive: false);
      final match = regex.firstMatch(sizeStr);
      
      if (match != null) {
        final value = double.parse(match.group(1)!);
        final unit = match.group(2)!.toUpperCase();
        
        switch (unit) {
          case 'KB': return (value * 1024).round();
          case 'MB': return (value * 1024 * 1024).round();
          case 'GB': return (value * 1024 * 1024 * 1024).round();
        }
      }
    } catch (e) {
      // 解析失败
    }
    return null;
  }
}
