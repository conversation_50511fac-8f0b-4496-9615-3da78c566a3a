import 'dart:io';

import 'package:app_rhyme/src/rust/api/cache/music_cache.dart';
import 'package:app_rhyme/src/rust/api/types/playinfo.dart';
import 'package:app_rhyme/utils/log_toast.dart';
import 'package:audio_service/audio_service.dart';
import 'package:app_rhyme/src/rust/api/bind/mirrors.dart';
import 'package:app_rhyme/src/rust/api/bind/type_bind.dart';
import 'package:app_rhyme/utils/global_vars.dart';
import 'package:app_rhyme/utils/quality_picker.dart';
import 'package:app_rhyme/utils/source_helper.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
// import 'package:http/http.dart' as http; // 不再需要，内置API已移除

// 这个结构代表了待播音乐的信息
class MusicContainer {
  late MusicAggregatorW aggregator;
  late MusicW currentMusic;
  late MusicInfo info;
  late String? extra;
  // 从Api or 本地获取的真实待播放的音质信息
  late Rx<Quality?> currentQuality;
  PlayInfo? playInfo;
  // 待播放的音频资源
  late AudioSource audioSource;
  // 已经使用过的音乐源，用于自动换源时选择下一个源
  List<String> usedSources = [];
  // 上次更新时间，用于判断是否需要更新
  DateTime lastUpdate = DateTime(1999);

  MusicContainer(MusicAggregatorW aggregator_) {
    aggregator = aggregator_;

    currentMusic = aggregator.getDefaultMusic();
    info = currentMusic.getMusicInfo();
    _updateQuality();
    audioSource = AudioSource.asset("assets/blank.mp3", tag: _toMediaItem());
  }

  // 使上次更新时间过期
  setOutdate() {
    lastUpdate = DateTime(1999);
  }

  String toCacheFileName() {
    return "${info.name}_${info.artist.join(",")}_${currentQuality.value!.short}.${currentQuality.value!.format ?? "unknown"}";
  }

  // 检查音乐是否需要更新
  bool shouldUpdate() {
    try {
      return (audioSource as ProgressiveAudioSource)
              .uri
              .path
              .contains("/assets/") ||
          DateTime.now().difference(lastUpdate).abs().inSeconds >= 1800;
    } catch (_) {
      return true;
    }
  }

  // 是否有缓存
  Future<bool> hasCache() async {
    try {
      return hasCachePlayinfo(musicInfo: info);
    } catch (e) {
      return false;
    }
  }

  // 更新音乐内部的播放信息和音频资源
  // quality: 指定音质，如果不指定则使用默认音质
  // 会在 主动获取 或者 LazyLoad 时使用
  // 如果获取失败，则会尝试换源
  // 如果换源后仍失败，则会返回false
  Future<bool> updateAll([Quality? quality]) async {
    bool success = await _updateAudioSource(quality);
    if (success) {
      await _updateLyric();
    }
    return success;
  }

  Future<PlayInfo?> getCurrentMusicPlayInfo([Quality? quality_]) async {
    late Quality finalQuality;
    if (quality_ != null) {
      // 用户明确指定了音质（如手动切换），直接使用用户指定的音质
      finalQuality = quality_;
      currentQuality.value = quality_;
      globalTalker.info("[getCurrentMusicPlayInfo] 用户指定音质: ${quality_.short}");
    } else if (currentQuality.value != null) {
      // 已有当前音质，验证是否仍然可用
      if (info.qualities.isNotEmpty) {
        finalQuality = smartPickQuality(info.qualities, currentQuality.value!.short);
        currentQuality.value = finalQuality;
        globalTalker.info("[getCurrentMusicPlayInfo] 验证当前音质: ${currentQuality.value!.short} -> 实际使用: ${finalQuality.short}");
      } else {
        finalQuality = currentQuality.value!;
      }
    } else {
      // 首次播放，严格按照用户偏好音质选择
      if (info.qualities.isNotEmpty) {
        QualityOption userPreferredOption = getUserPreferredQualityOption();
        String apiQuality = qualityOptionToApiString(userPreferredOption);
        finalQuality = smartPickQuality(info.qualities, apiQuality);
        currentQuality.value = finalQuality;
        globalTalker.info("[getCurrentMusicPlayInfo] 严格按照用户偏好: $userPreferredOption ($apiQuality) -> 实际使用: ${finalQuality.short}");
      } else {
        LogToast.error("获取播放信息失败", "未找到可用音质",
            "[getCurrentMusicPlayInfo] Failed to get play info, no quality found");
        return null;
      }
    }
    // 更新extra信息
    extra = currentMusic.getExtraInfo(quality: finalQuality);

    // // 有本地缓存直接返回
    try {
      playInfo = await getCachePlayinfo(musicInfo: info);
      if (playInfo != null) {
        globalTalker.info("[getCurrentMusicPlayInfo] 使用缓存歌曲: ${info.name}");
        currentQuality.value = playInfo!.quality;
        return playInfo!;
      }
      // ignore: empty_catches
    } catch (e) {}

    globalTalker.info('[MusicContainer] 获取播放信息: ${info.name}, Source: ${info.source}, Extra: $extra');

    // 只允许通过JS音乐源获取播放链接
    if (globalJsMusicSourceExecutor != null) {
      try {
        playInfo = await globalJsMusicSourceExecutor!.getMusicPlayInfo(info.source, extra!);
        if (playInfo != null) {
          currentQuality.value = playInfo!.quality;

          // 获取歌词数据并更新到MusicInfo
          final lyricData = globalJsMusicSourceExecutor!.getLastLyricData();
          if (lyricData != null) {
            if (lyricData['lyric']?.isNotEmpty == true) {
              info.lyric = lyricData['lyric'];
              globalTalker.info("[getCurrentMusicPlayInfo] 更新歌词成功: ${info.name}");
            }
            if (lyricData['tlyric']?.isNotEmpty == true) {
              info.tlyric = lyricData['tlyric'];
              globalTalker.info("[getCurrentMusicPlayInfo] 更新翻译歌词成功: ${info.name}");
            }
          }

          globalTalker.info(
              "[getCurrentMusicPlayInfo] 使用JS音乐源获取playinfo: [${info.source}]${info.name}");
          return playInfo;
        }
      } catch (e) {
        globalTalker.error("[getCurrentMusicPlayInfo] JS音乐源获取失败: $e");
      }
    }

    // 没有本地缓存，检查是否有JS音乐源可用
    if (globalJsMusicSourceExecutor != null) {
      globalTalker.error("[getCurrentMusicPlayInfo] JS音乐源可用但获取播放信息失败");
      LogToast.error("无法播放", "当前音质不可用，请尝试其他音质或检查网络连接",
          "[getCurrentMusicPlayInfo] JS music source available but failed to get play info");
    } else {
      // 未导入任何音乐源，提示用户
      LogToast.error("无法播放", "请先导入JS音乐源",
          "[getCurrentMusicPlayInfo] No music source available");
    }
    return null;
  }



  // 将音乐信息转化为MediaItem, 用于AudioService在系统显示音频信息
  MediaItem _toMediaItem() {
    Uri? artUri;
    try {
      if (info.artPic != null && info.artPic!.isNotEmpty) {
        artUri = Uri.parse(info.artPic!);
      }
    } catch (e) {
      // 如果封面URL解析失败，使用null
      artUri = null;
    }

    return MediaItem(
      id: extra?.hashCode.toString() ?? info.name.hashCode.toString(),
      title: info.name,
      album: info.album ?? '',
      artUri: artUri,
      artist: info.artist.isNotEmpty ? info.artist.join(", ") : 'Unknown Artist',
      duration: Duration.zero, // 初始时长为0，播放时会自动更新
    );
  }

  Future<void> _updateLyric() async {
    if (info.lyric == null || info.lyric!.isEmpty) {
      try {
        var lyric = await aggregator.fetchLyric();
        globalTalker.info("[MusicContainer] 更新 '${info.name}' 歌词成功");
        info.lyric = lyric;
      } catch (e) {
        LogToast.error("更新歌词失败", "在线更新歌词失败: $e",
            "[MusicContainer] Failed to update lyric: $e");
        info.lyric = "[00:00.00]获取歌词失败";
      }
    }
  }

  Future<bool> _updateAudioSource([Quality? quality]) async {
    lastUpdate = DateTime.now();
    if (quality != null) extra = currentMusic.getExtraInfo(quality: quality);
    while (true) {
      try {
        playInfo = await getCurrentMusicPlayInfo(quality);
      } catch (e) {
        playInfo = null;
      }
      if (playInfo != null) {
        // 更新当前音质 - 如果用户指定了音质，使用用户指定的音质，否则使用API返回的音质
        if (quality != null) {
          currentQuality.value = quality;
        } else {
          currentQuality.value = playInfo!.quality;
        }

        if (playInfo!.uri.contains("http")) {
          if ((Platform.isIOS || Platform.isMacOS) &&
              ((playInfo!.quality.format != null &&
                      playInfo!.quality.format!.contains("flac")) ||
                  (playInfo!.quality.short.contains("flac")))) {
            audioSource = ProgressiveAudioSource(Uri.parse(playInfo!.uri),
                tag: _toMediaItem(),
                options: const ProgressiveAudioSourceOptions(
                    darwinAssetOptions: DarwinAssetOptions(
                        preferPreciseDurationAndTiming: true)));
          } else {
            audioSource = AudioSource.uri(Uri.parse(playInfo!.uri), tag: _toMediaItem());
          }
        } else {
          if ((Platform.isIOS || Platform.isMacOS) &&
              ((playInfo!.quality.format != null &&
                      playInfo!.quality.format!.contains("flac")) ||
                  (playInfo!.quality.short.contains("flac")))) {
            audioSource = ProgressiveAudioSource(Uri.file(playInfo!.uri),
                tag: _toMediaItem(),
                options: const ProgressiveAudioSourceOptions(
                    darwinAssetOptions: DarwinAssetOptions(
                        preferPreciseDurationAndTiming: true)));
          } else {
            audioSource = AudioSource.file(playInfo!.uri, tag: _toMediaItem());
          }
        }
        globalTalker.info("[MusicContainer] 更新 '${info.name}' 音频资源成功");
        return true;
      } else {
        // 播放信息获取失败，尝试换源播放
        LogToast.error("更新播放资源失败", "${info.name}更新播放资源失败, 尝试换源播放",
            "[MusicContainer] Failed to update audio source, try to change source");
        bool changed = await _changeSource();
        if (!changed) {
          return false;
        }
      }
    }
  }

  Future<bool> _changeSource([String? source]) async {
    // 换源表明弃用当前源，将其移到usedSource中
    usedSources.add(currentMusic.source());
    // 根据usedSource来获得下一个源
    source ??= nextSource(usedSources);

    if (source != null) {
      try {
        var musics = await aggregator.fetchMusics(sources: [source]);
        if (musics.isEmpty) {
          LogToast.error(
              "切换音乐源失败",
              "${info.name}切换音乐源失败: 在$source查找不到'${info.name}'歌曲.",
              "[MusicContainer] Failed to change music source: Cannot find '${info.name}' in $source");
          return false;
        }
        await aggregator.setDefaultSource(source: source);
        currentMusic = aggregator.getDefaultMusic();
        info = currentMusic.getMusicInfo();
        extra = currentMusic.getExtraInfo(quality: info.defaultQuality!);
        audioSource = AudioSource.asset("assets/blank.mp3", tag: _toMediaItem());
        LogToast.info("切换音乐源成功", "${info.name}默认音源切换为$source",
            "[MusicContainer] Successfully changed music source to $source");
      } catch (e) {
        LogToast.error("切换音乐源失败", "${info.name}切换音乐源失败: $e",
            "[MusicContainer] Failed to change music source: $e");

        return false;
      }
      return true;
    } else {
      return false;
    }
  }

  void _updateQuality([Quality? quality]) {
    if (quality != null) {
      currentQuality.value = quality;
      extra = currentMusic.getExtraInfo(quality: quality);
    } else {
      // 初始化时设置为null的Rx对象，避免在启动时进行音质选择
      currentQuality = Rx<Quality?>(null);
      extra = null;
    }
  }

  // 内置网易云音乐API已移除，现在只使用JS音乐源和第三方API

  // 内置播放源相关的辅助函数已移除

  /// 重写相等性比较，用于HeartButton等组件检测变化
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! MusicContainer) return false;

    // 使用与FavoritesHelper._isSameMusic相同的比较逻辑
    return info.name == other.info.name &&
           info.artist.join(",") == other.info.artist.join(",") &&
           info.album == other.info.album &&
           info.duration == other.info.duration &&
           aggregator.getDefaultMusic().getMusicInfo().source ==
           other.aggregator.getDefaultMusic().getMusicInfo().source;
  }

  @override
  int get hashCode {
    return Object.hash(
      info.name,
      info.artist.join(","),
      info.album,
      info.duration,
      aggregator.getDefaultMusic().getMusicInfo().source,
    );
  }

  // 添加缺失的方法作为占位符
  Quality smartPickQuality(List<Quality> qualities, String preferredQuality) {
    // TODO: Implement smart quality picking logic
    if (qualities.isNotEmpty) {
      return qualities.first;
    }
    // 返回一个默认质量
    return const Quality(short: "128", format: "mp3", size: "0");
  }

  dynamic getUserPreferredQualityOption() {
    // TODO: Implement user preferred quality option logic
    return "medium"; // 默认返回中等质量
  }
}
